import { Dialog, Transition } from "@headlessui/react";
import { Fragment, useState } from "react";
import { router } from "@inertiajs/react";
import { toast } from "react-toastify";
import setDefaultDateFormat from "@/Util/setDefaultDateFormat";

export default function CancelDomainDeletionModal({ isOpen, onClose, deletionRequest }) {
    const [submitting, setSubmitting] = useState(false);
    const [inputValue, setInputValue] = useState("");
    const [isValid, setIsValid] = useState(false);
    const [showConfirmStep, setShowConfirmStep] = useState(false);

    const handleInputChange = (e) => {
        const value = e.target.value;
        setInputValue(value);
        setIsValid(value.trim().toLowerCase() === deletionRequest.email.toLowerCase());
    };

    const handleSubmit = () => {
        setShowConfirmStep(true);
    };

    const handleConfirm = () => {
        if (!isValid) return;

        setSubmitting(true);

        axios.post(
            route("domain.delete-request.cancel"),
            {
                domainName: deletionRequest.domainName,
                userEmail: deletionRequest.email,
                domainId: deletionRequest.domain_id,
                createdDate: deletionRequest.created_at,
                userID: deletionRequest.user_id,
                email: inputValue,
            },
        )
        .then((response) => {
                toast.success("Domain deletion request cancelled successfully.");
                onClose();
                setSubmitting(false);
                router.visit(route("domain.delete-request.view"));
                return response;
            })
            .catch((error) => {
                setShowConfirmStep(false);
                setSubmitting(false);
                console.log(error.response);
                return error.response;
            });
    };

    const handleClose = () => {
        setInputValue("");
        setIsValid(false);
        setShowConfirmStep(false);
        setSubmitting(false);
        onClose();
    };

    return (
        <Transition appear show={isOpen} as={Fragment}>
            <Dialog as="div" className="relative z-10" onClose={handleClose}>
                <Transition.Child
                    as={Fragment}
                    enter="ease-out duration-300"
                    enterFrom="opacity-0"
                    enterTo="opacity-100"
                    leave="ease-in duration-200"
                    leaveFrom="opacity-100"
                    leaveTo="opacity-0"
                >
                    <div className="fixed inset-0 bg-black bg-opacity-25" />
                </Transition.Child>

                <div className="fixed inset-0 overflow-y-auto">
                    <div className="flex min-h-full items-center justify-center p-4 text-center">
                        <Transition.Child
                            as={Fragment}
                            enter="ease-out duration-300"
                            enterFrom="opacity-0 scale-95"
                            enterTo="opacity-100 scale-100"
                            leave="ease-in duration-200"
                            leaveFrom="opacity-100 scale-100"
                            leaveTo="opacity-0 scale-95"
                        >
                            <Dialog.Panel className="w-full max-w-md transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all">
                                {!showConfirmStep ? (
                                    <>
                                        <Dialog.Title
                                            as="h3"
                                            className="text-lg font-medium leading-6 text-gray-900 mb-4"
                                        >
                                            Cancel Domain Deletion Request
                                        </Dialog.Title>
                                        <div className="mt-2">
                                            <p className="text-sm text-gray-500 mb-4">
                                                You are about to cancel the approved deletion request for{" "}
                                                <strong>{deletionRequest.domainName}</strong>.
                                            </p>
                                            <p className="text-sm text-gray-500 mb-4">
                                                This will revert the request back to pending status and the domain will not be deleted.
                                            </p>
                                            <p className="text-sm text-gray-500 mb-4">
                                                <strong>Domain:</strong> {deletionRequest.domainName}<br />
                                                <strong>User:</strong> {deletionRequest.first_name} {deletionRequest.last_name}<br />
                                                <strong>Email:</strong> {deletionRequest.email}<br />
                                                <strong>Request Date:</strong> {setDefaultDateFormat(deletionRequest.requested_at)}<br />
                                                <strong>Approved Date:</strong> {setDefaultDateFormat(deletionRequest.feedback_date)}
                                            </p>
                                            <p className="text-sm text-red-600 mb-4">
                                                To confirm cancellation, please type the user's email address below:
                                            </p>
                                            <input
                                                type="email"
                                                value={inputValue}
                                                onChange={handleInputChange}
                                                placeholder="Enter user's email address"
                                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
                                            />
                                        </div>

                                        <div className="mt-6 flex justify-end space-x-3">
                                            <button
                                                type="button"
                                                className="inline-flex justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
                                                onClick={handleClose}
                                            >
                                                Cancel
                                            </button>
                                            <button
                                                type="button"
                                                className={`inline-flex justify-center rounded-md border border-transparent px-4 py-2 text-sm font-medium text-white focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 ${
                                                    isValid
                                                        ? "bg-red-600 hover:bg-red-700"
                                                        : "bg-gray-400 cursor-not-allowed"
                                                }`}
                                                onClick={handleSubmit}
                                                disabled={!isValid}
                                            >
                                                Continue
                                            </button>
                                        </div>
                                    </>
                                ) : (
                                    <>
                                        <Dialog.Title
                                            as="h3"
                                            className="text-lg font-medium leading-6 text-red-900 mb-4"
                                        >
                                            Final Confirmation
                                        </Dialog.Title>
                                        <div className="mt-2">
                                            <p className="text-sm text-red-600 mb-4">
                                                <strong>WARNING:</strong> You are about to cancel the approved deletion request for{" "}
                                                <strong>{deletionRequest.domainName}</strong>.
                                            </p>
                                            <p className="text-sm text-gray-700 mb-4">
                                                This action will:
                                            </p>
                                            <ul className="text-sm text-gray-700 mb-4 list-disc list-inside">
                                                <li>Revert the request back to pending status</li>
                                                <li>Remove the approval flag</li>
                                                <li>Prevent the domain from being deleted by the cron job</li>
                                                <li>Send a notification to the user</li>
                                            </ul>
                                            <p className="text-sm text-red-600 mb-4">
                                                Are you absolutely sure you want to proceed?
                                            </p>
                                        </div>

                                        <div className="mt-6 flex justify-end space-x-3">
                                            <button
                                                type="button"
                                                className="inline-flex justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
                                                onClick={() => setShowConfirmStep(false)}
                                                disabled={submitting}
                                            >
                                                Go Back
                                            </button>
                                            <button
                                                type="button"
                                                className="inline-flex justify-center rounded-md border border-transparent bg-red-600 px-4 py-2 text-sm font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
                                                onClick={handleConfirm}
                                                disabled={submitting}
                                            >
                                                {submitting ? "Cancelling..." : "Cancel Deletion Request"}
                                            </button>
                                        </div>
                                    </>
                                )}
                            </Dialog.Panel>
                        </Transition.Child>
                    </div>
                </div>
            </Dialog>
        </Transition>
    );
}

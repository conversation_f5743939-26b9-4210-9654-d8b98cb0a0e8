import CursorPaginate from "@/Components/Util/CursorPaginate";
import { getEventValue } from "@/Util/TargetInputEvent";
import { useState } from "react";
import { router } from "@inertiajs/react";
import { ImSortAlphaAsc, ImSortAlphaDesc } from "react-icons/im";
import { MdOutlineSettings, MdOutlineFilterAlt } from "react-icons/md";
import { TbSortAscending2, TbSortDescending2 } from "react-icons/tb";
import Item from "../../Components/WireTransfer/Item";
import Filter from "../../Components/WireTransfer/Filter";
import AdminLayout from "../../Layouts/AdminLayout";

export default function Index({
    items,
    onFirstPage,
    onLastPage,
    nextPageUrl,
    previousPageUrl,
    itemCount = 0,
    total = 0,
}) {
    const SORT_TYPE = {
        NAME_ASC: "name:asc",
        NAME_DESC: "name:desc",
        CREATED_ASC: "created_at:asc",
        CREATED_DESC: "created_at:desc",
        UPDATED_ASC: "updated_at:asc",
        UPDATED_DESC: "updated_at:desc",
    };

    // console.log(items);
    const [selectedItems, setSelectedItems] = useState([]);
    const [limit, setLimit] = useState(route().params.limit || 10); // Added limit state

    const paramOrderBy = route().params.orderby;

    const handleItemCheckboxChange = (itemId, e) => {
        if (getEventValue(e)) {
            setSelectedItems((prevSelectedItems) => {
                return [...prevSelectedItems, itemId];
            });
            setSelectAll(() => items.length === selectedItems.length + 1);
        } else {
            setSelectedItems((prevSelectedItems) =>
                prevSelectedItems.filter((id) => id !== itemId)
            );
            setSelectAll(false);
        }
    };

    const handleSortOrder = (sortOrder) => {
        let payload = {};

        payload.orderby = sortOrder;

        router.get(route("billing.wire.transfer"), payload);
    };

    // Handle limit change
    const handleLimitChange = (e) => {
        const newLimit = getEventValue(e);
        setLimit(newLimit);
        router.get(route("billing.wire.transfer"), {
            ...route().params,
            limit: newLimit,
        }, { preserveState: true });
    };

    return (
        <AdminLayout> 
            {/* hideNav={true} */}
            <div className="mx-auto container max-w-[900px] mt-20 flex flex-col space-y-4">
                <h2 className="text-4xl font-bold">Wire Transfer</h2>
                <div className="flex justify-start">
                    <label className="mr-2 text-sm pt-1 text-gray-600">
                        Show
                    </label>
                    <select
                        value={limit}
                        onChange={handleLimitChange}
                        className="border border-gray-300 rounded px-4 py-1 text-sm w-20"
                    >
                        {[20, 25, 30, 40, 50, 100].map((val) => (
                            <option key={val} value={val}>
                                {val}
                            </option>
                        ))}
                    </select>
                </div>
                <div
                    id="sample"
                    className="flex items-center space-x-2 flex-wrap min-h-[2rem]"
                >
                    
                    <label className="flex items-center">
                        <MdOutlineFilterAlt />
                        <span className="ml-2 text-sm text-gray-600">
                            {" "}
                            Filter:{" "}
                        </span>
                    </label>
                    <Filter />
                </div>
                
                <div>
                    <table className="min-w-[1200px] text-left border-spacing-y-2.5 border-separate">
                        <thead className=" bg-gray-50 text-sm">
                            <tr>
                                <th className="w-[8%] py-3">
                                    <label className="flex items-center pl-2 space-x-2">
                                        <span>Name</span>
                                        <button
                                            onClick={() =>
                                                handleSortOrder(
                                                    paramOrderBy ===
                                                        SORT_TYPE.NAME_ASC
                                                        ? SORT_TYPE.NAME_DESC
                                                        : SORT_TYPE.NAME_ASC
                                                )
                                            }
                                            disabled={items.length === 0}
                                        >
                                            {paramOrderBy ===
                                            SORT_TYPE.NAME_ASC ? (
                                                <ImSortAlphaAsc />
                                            ) : (
                                                <ImSortAlphaDesc />
                                            )}
                                        </button>
                                    </label>
                                </th>
                                <th className="w-[7%]">
                                    <span>Amount</span>
                                </th>
                                <th className="w-[10%]">
                                    <span>Received Amount</span>
                                </th>
                                <th className="w-[7%]">
                                    <span>Company</span>
                                </th>
                                <th className="w-[7%]">
                                    <span>Status</span>
                                </th>
                                <th className="w-[10%]">
                                    <span>Note</span>
                                </th>
                                <th className="w-[14%]">
                                    <label className="flex items-center space-x-2">
                                        <span>Date Created</span>
                                        <button
                                            onClick={() =>
                                                handleSortOrder(
                                                    paramOrderBy ===
                                                        SORT_TYPE.CREATED_ASC
                                                        ? SORT_TYPE.CREATED_DESC
                                                        : SORT_TYPE.CREATED_ASC
                                                )
                                            }
                                            disabled={items.length === 0}
                                        >
                                            {paramOrderBy ===
                                            SORT_TYPE.CREATED_ASC ? (
                                                <TbSortAscending2 />
                                            ) : (
                                                <TbSortDescending2 />
                                            )}
                                        </button>
                                    </label>
                                </th>
                                <th className="w-[14%]">
                                    <label className="flex items-center space-x-2">
                                        <span>Date Updated</span>
                                        <button
                                            onClick={() =>
                                                handleSortOrder(
                                                    paramOrderBy ===
                                                        SORT_TYPE.UPDATED_ASC
                                                        ? SORT_TYPE.UPDATED_DESC
                                                        : SORT_TYPE.UPDATED_ASC
                                                )
                                            }
                                            disabled={items.length === 0}
                                        >
                                            {paramOrderBy ===
                                            SORT_TYPE.UPDATED_ASC ? (
                                                <TbSortAscending2 />
                                            ) : (
                                                <TbSortDescending2 />
                                            )}
                                        </button>
                                    </label>
                                </th>
                                <th className="w-[14%]">
                                    <span className="text-xl">
                                        <MdOutlineSettings />
                                    </span>
                                </th>
                            </tr>
                        </thead>
                        <tbody className="text-sm">
                            {items.map((item, index) => (
                                <Item
                                    key={"ci-" + index}
                                    item={item}
                                    isSelected={selectedItems.includes(item.id)}
                                    onCheckboxChange={handleItemCheckboxChange}
                                />
                            ))}
                        </tbody>
                    </table>
                </div>

                {/* Pagination */}
                <CursorPaginate
                    onFirstPage={onFirstPage}
                    onLastPage={onLastPage}
                    nextPageUrl={nextPageUrl}
                    previousPageUrl={previousPageUrl}
                    itemCount={itemCount}
                    total={total}
                    shouldPreserveState={true}
                />
            </div>
        </AdminLayout>
    );
}

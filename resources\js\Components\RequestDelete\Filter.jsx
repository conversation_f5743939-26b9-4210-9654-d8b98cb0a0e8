import ActiveFilter from "@/Components/Util/Filter/ActiveFilter";
import DisplayFilter from "@/Components/Util/Filter/DisplayFilter";
import OptionFilter from "@/Components/Util/Filter/OptionFilter";
import TextFilter from "@/Components/Util/Filter/TextFilter";
import useOutsideClick from "@/Util/useOutsideClick";
import { useRef, useState } from "react";
import {
    offFilter,
    updateFieldValue,
} from "@/Components/Util/Filter/FilterMethod";
import { router } from "@inertiajs/react";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { useEffect } from "react";

export default function Filter({ status }) {
    const { orderby, user, email } = route().params;

    const containerRef = useRef();

    const [userInput, setUserInput] = useState(user || "");
    const [emailInput, setEmailInput] = useState(email || "");
    const [deletedByInput, setDeletedByInput] = useState("");

    const generateConfig = () => {
        const cfg = {
            container: {
                active: false,
                reload: false,
            },
            field: {
                orderby: {
                    active: false,
                    value: orderby ? [orderby] : [],
                    type: "option",
                    items: [
                        "domain:desc",
                        "domain:asc",
                        "requested_at:desc",
                        "requested_at:asc",
                        "feedback_date:desc",
                        "feedback_date:asc",
                        "email:desc",
                        "email:asc",
                    ],
                    name: "Order By",
                },
                email: {
                    active: false,
                    value: email ? [email] : [],
                    type: "text",
                    name: "Email",
                    tempValue: emailInput,
                },


            },
        };

        // if (status === "DELETED") {
        //     cfg.field.deletedBy = {
        //         active: false,
        //         value: [deletedByInput],
        //         type: "text",
        //         name: "Deleted By",
        //         tempValue: deletedByInput,
        //     };
        //     cfg.field.orderby.items.push("dateDeleted:desc", "dateDeleted:asc");
        // }

        return cfg;
    };

    const [filter, setFilter] = useState(generateConfig());
    const { field } = filter;

    // ⚠️ Add this useEffect to reset filters when the tab/status changes
    useEffect(() => {
        setFilter(generateConfig());
    }, [status]);

    useOutsideClick(containerRef, () => {
        setFilter((prevFilter) => {
            const updatedFilter = offFilter(prevFilter);
            return {
                ...updatedFilter,
                field: Object.keys(updatedFilter.field).reduce(
                    (acc, key) => ({
                        ...acc,
                        [key]: {
                            ...updatedFilter.field[key],
                            active: false,
                        },
                    }),
                    {}
                ),
            };
        });
    });

    const submit = (updatedFilter) => {
        let { orderby, email, deletedBy } = updatedFilter.field;
        let payload = { statusType: status };

        if (orderby.value.length > 0) payload.orderby = orderby.value[0];
        if (email.value.length > 0) payload.email = email.value[0];
        if (deletedBy?.value.length > 0) payload.deletedBy = deletedBy.value[0];

        router.get(route("domain.delete-request.view"), payload);
    };


    const handleDisplayToggle = (newObject) => {
        const closedFilter = offFilter(filter);
        setFilter({
            ...closedFilter,
            ...newObject,
        });
    };

    const handleFieldUpdateValue = (key, value) => {
        const setInputMap = {
            user: setUserInput,
            email: setEmailInput,
            deletedBy: setDeletedByInput,
        };

        if (setInputMap[key]) {
            setInputMap[key](value);

            if (!value || value === filter.field[key].tempValue) {
                const newValue = updateFieldValue(value, {
                    ...filter.field[key],
                });
                const updatedFilter = {
                    ...filter,
                    container: { ...filter.container, active: false },
                    field: {
                        ...filter.field,
                        [key]: { ...newValue },
                    },
                };
                setFilter(offFilter(updatedFilter));
                submit(updatedFilter);
                return;
            }

            setFilter((prevFilter) => ({
                ...prevFilter,
                field: {
                    ...prevFilter.field,
                    [key]: {
                        ...prevFilter.field[key],
                        tempValue: value,
                    },
                },
            }));
            return;
        }

        const newValue = updateFieldValue(value, { ...filter.field[key] });
        const updatedFilter = {
            ...filter,
            container: { ...filter.container, active: false },
            field: {
                ...filter.field,
                [key]: { ...newValue },
            },
        };

        setFilter(offFilter(updatedFilter));
        submit(updatedFilter);
    };

    return (
        <div className="flex items-center relative" ref={containerRef}>
            <ActiveFilter
                field={field}
                handleFieldUpdateValue={handleFieldUpdateValue}
            />
            <div className="relative">
                <DisplayFilter
                    handleDisplayToggle={handleDisplayToggle}
                    container={filter.container}
                    field={filter.field}
                />

                <OptionFilter
                    fieldProp={field.orderby}
                    fieldKey="orderby"
                    handleFieldUpdateValue={handleFieldUpdateValue}
                />

                {field.deletedBy && (
                    <TextFilter
                        fieldProp={field.deletedBy}
                        fieldKey="deletedBy"
                        handleFieldUpdateValue={handleFieldUpdateValue}
                        offFilter={() => {
                            const currentValue =
                                field.deletedBy.tempValue ||
                                field.deletedBy.value[0] ||
                                "";
                            handleFieldUpdateValue("deletedBy", currentValue);
                            setFilter(offFilter(filter));
                        }}
                        placeholder="Search Deleted By"
                    />
                )}
                <TextFilter
                    fieldProp={field.email}
                    fieldKey="email"
                    handleFieldUpdateValue={handleFieldUpdateValue}
                    offFilter={() => {
                        const currentValue =
                            field.email.tempValue || field.email.value[0] || "";
                        handleFieldUpdateValue("email", currentValue);
                        setFilter(offFilter(filter));
                    }}
                />
            </div>
        </div>
    );
}

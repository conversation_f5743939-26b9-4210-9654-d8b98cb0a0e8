//* PACKAGES
import React, {useState, useEffect, useRef} from 'react'
import { Link, router } from '@inertiajs/react';
import { toast } from 'react-toastify';
import axios from 'axios';

//* ICONS
import { MdMoreVert } from "react-icons/md";

//* COMPONENTS
import DropDownContainer from "@/Components/DropDownContainer";
import Checkbox from "@/Components/Checkbox";

//* PARTIALS
//...

//* STATE
//...

//* HOOKS 
import { usePermissions } from '@/Hooks/usePermissions';

//* UTILS
import useOutsideClick from "@/Util/useOutsideClick";
import setDefaultDateFormat from "../../Util/setDefaultDateFormat";

//* ENUMS
//...

//* CONSTANTS
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...

export default function Item(
    {
        item,
        isSelected,
        onCheckboxChange,   
    }
) 
{
    //! PACKAGE
    const ref = useRef();
    
    //! HOOKS
    const { hasPermission } = usePermissions();
    
    //! VARIABLES
    //...

    //! STATES
    const [show, setShow] = useState(false);

    //! USE EFFECTS
    //...

    //! FUNCTIONS
    useOutsideClick(ref, () => {
        setShow(false);
    });

    const handleCheckboxChange = (e) => {
        onCheckboxChange(item.id, e);
    };

    const onHandleOnClick = (link, method, data) => {
        setShow(false);
        router.visit(link, { method: method, data: data });
    };

    const onHandleDelete = (domainCount, id) => {
        setShow(false);
        if (domainCount > 0) {
            router.post(route("category.warn"), { ids: [id] });
        }
        else {
            router.delete(
                route("category.delete", { ids: [id] }),
                { onSuccess: () => toast.success("Category Has Successfully Been Deleted.") }
            );
        }
    }

    const getStatus = () => {
        if (item.deleted_at) {
            return 'Rejected';
        } else if (item.verified_at) {
            return 'Verified';
        } else if (item.reviewed_at && !item.deleted_at) {
            return 'Unverified';
        } else if (!item.reviewed_at && !item.deleted_at) {
            return 'Pending';
        }
    };

    const showActionButton = () => {
        const status = getStatus();
        return status === 'Pending' || status === 'Unverified';
    };

    const rowActions = 
    [
        {
            label           : 'verify',
            hasAccess       : hasPermission('billing.wire.transfer.verify-edit'),
            shouldDisplay   : true,
            handleEventClick: () =>
            {
                router.get(route("billing.wire.transfer.verify-edit", { id: [item.id] }));
            } 
        },
    ];

    const permittedActions = rowActions.filter(rowAction => rowAction.hasAccess && rowAction.shouldDisplay);

    return (
        <tr className="hover:bg-gray-100">
            <td>
                <div className="flex items-center pl-2 space-x-2">
                    {/* <Checkbox
                        name="name"
                        value={item.name}
                        checked={isSelected}
                        handleChange={handleCheckboxChange}
                    /> */}
                    <div title={item.account_name}>
                        <span>{item.account_name}</span>
                    </div>
                </div>
            </td>
            <td>
                <span>{parseFloat(item.amount).toFixed(2)}</span>
            </td>
            <td>
                <span>{parseFloat(item.net_amount).toFixed(2)}</span>
            </td>
            <td>
                <span>{item.company}</span>
            </td>
            <td>
                <span>{getStatus()}</span>
            </td>
            <td>
                <span>{item.note}</span>
            </td>
            <td>
                <span>{setDefaultDateFormat(item.created_at) + ' ' + new Date(item.created_at + 'Z').toLocaleTimeString()}</span>
            </td>
            <td>
                <span>{setDefaultDateFormat(item.updated_at) + ' ' + new Date(item.updated_at + 'Z').toLocaleTimeString()}</span>
            </td>
            <td>
                {showActionButton() && <span ref={ref} className="relative">
                    <button
                        className="flex items-center"
                        onClick={() => setShow(!show)}
                    >
                        <MdMoreVert className="cursor-pointer text-2xl rounded-full hover:bg-gray-200" />
                    </button>
                    <DropDownContainer show={show}>
                        {
                            permittedActions.length == 0
                                ?
                                    <div
                                        className='text-sm font-medium rounded-md p-2 text-danger'
                                    >
                                        No Actions Permitted
                                    </div>
                                :
                                    permittedActions.map(
                                        (rowAction, rowActionIndex) =>
                                        {
                                            return (
                                                <button
                                                    key={rowActionIndex}
                                                    className="hover:bg-gray-100 px-5 py-1 w-full text-left capitalize"
                                                    onClick={rowAction.handleEventClick}
                                                >
                                                    {rowAction.label}
                                                </button>
                                            );
                                        }
                                    )
                        }
                    </DropDownContainer>
                </span>}
            </td>
        </tr>
    );
}
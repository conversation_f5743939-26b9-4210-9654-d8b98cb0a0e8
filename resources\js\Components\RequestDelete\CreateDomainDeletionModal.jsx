import { Dialog, Transition } from "@headlessui/react";
import { Fragment, useState } from "react";
import { router } from "@inertiajs/react";
import { toast } from "react-toastify";
import PrimaryButton from "@/Components/PrimaryButton";
import SecondaryButton from "@/Components/SecondaryButton";
import DangerButton from "@/Components/DangerButton";
import Checkbox from "@/Components/Checkbox";

export default function CreateDomainDeletionModal({ isOpen, onClose, domain }) {
    const [submitting, setSubmitting] = useState(false);
    const [inputValue, setInputValue] = useState("");
    const [isValid, setIsValid] = useState(false);
    const [selectedReason, setSelectedReason] = useState("");
    const [customReason, setCustomReason] = useState("");
    const [agreePolicy, setAgreePolicy] = useState(false);
    const [agreeGrace, setAgreeGrace] = useState(false);
    const [errors, setErrors] = useState({});

    const reasonOptions = [
        "No Longer Needed",
        "Mistaken Registration", 
        "Rebranding",
        "Cost Savings",
        "Trademark/Legal Issues",
        "Low Performance",
        "Security/Privacy Concerns",
        "Portfolio Cleanup",
        "Others"
    ];

    const handleInputChange = (e) => {
        const value = e.target.value;
        setInputValue(value);
        setIsValid(value.trim().toLowerCase() === domain.name.toLowerCase());
    };

    const handleReasonChange = (e) => {
        setSelectedReason(e.target.value);
        if (e.target.value !== "Others") {
            setCustomReason("");
        }
    };

    const handleSubmit = () => {
        if (!isValid) return;
        if (!selectedReason) {
            setErrors({ reason: ["Please select a reason for deletion"] });
            return;
        }
        if (selectedReason === "Others" && !customReason.trim()) {
            setErrors({ customReason: ["Please provide a custom reason"] });
            return;
        }
        if (!agreePolicy || !agreeGrace) {
            setErrors({ policy: ["Please agree to both policies"] });
            return;
        }

        setSubmitting(true);
        setErrors({});

        const finalReason = selectedReason === "Others" ? customReason : selectedReason;

        router.post(
            route("domain.delete-request.delete"),
            {
                domainName: domain.name,
                userEmail: domain.domain_email,
                domainId: domain.id,
                createdDate: domain.created_at,
                userID: domain.userId,
                reason: finalReason,
            },
            {
                onSuccess: () => {
                    toast.success("Domain deletion request created successfully.");
                    onClose();
                    setSubmitting(false);
                    router.visit(route("domain.delete-request.view") + "?statusType=APPROVED");
                },
                onError: (errors) => {
                    setErrors(errors);
                    setSubmitting(false);
                    toast.error("Failed to create deletion request. Please try again.");
                },
            }
        );
    };

    const handleClose = () => {
        setInputValue("");
        setIsValid(false);
        setSelectedReason("");
        setCustomReason("");
        setAgreePolicy(false);
        setAgreeGrace(false);
        setErrors({});
        setSubmitting(false);
        onClose();
    };

    if (!domain) return null;

    return (
        <Transition appear show={isOpen} as={Fragment}>
            <Dialog as="div" className="relative z-10" onClose={handleClose}>
                <Transition.Child
                    as={Fragment}
                    enter="ease-out duration-300"
                    enterFrom="opacity-0"
                    enterTo="opacity-100"
                    leave="ease-in duration-200"
                    leaveFrom="opacity-100"
                    leaveTo="opacity-0"
                >
                    <div className="fixed inset-0 bg-black bg-opacity-25" />
                </Transition.Child>

                <div className="fixed inset-0 overflow-y-auto">
                    <div className="flex min-h-full items-center justify-center p-4 text-center">
                        <Transition.Child
                            as={Fragment}
                            enter="ease-out duration-300"
                            enterFrom="opacity-0 scale-95"
                            enterTo="opacity-100 scale-100"
                            leave="ease-in duration-200"
                            leaveFrom="opacity-100 scale-100"
                            leaveTo="opacity-0 scale-95"
                        >
                            <Dialog.Panel className="w-full max-w-2xl transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all">
                                <Dialog.Title
                                    as="h3"
                                    className="text-xl font-semibold leading-6 text-primary mb-4"
                                >
                                    Delete Domain: {domain.name}
                                </Dialog.Title>

                                <div className="mt-2">
                                    <p className="text-sm text-gray-600 mb-6">
                                        To delete {domain.name}, please provide a detailed reason for our review.
                                    </p>

                                    {/* Domain Confirmation Input */}
                                    <div className="mb-4">
                                        <label className="block text-sm font-medium text-gray-700 mb-2">
                                            Type {domain.name} to confirm <span className="text-red-500">*</span>
                                        </label>
                                        <input
                                            type="text"
                                            value={inputValue}
                                            onChange={handleInputChange}
                                            placeholder={`Type "${domain.name}" here`}
                                            className="w-full px-3 py-2 border-2 border-primary rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                                        />
                                        {errors.domain && (
                                            <p className="text-sm text-red-600 mt-1">{errors.domain[0]}</p>
                                        )}
                                    </div>

                                    {/* Reason Selection */}
                                    <div className="mb-4">
                                        <label className="block text-sm font-medium text-gray-700 mb-2">
                                            Reason for Deletion <span className="text-red-500">*</span>
                                        </label>
                                        <select
                                            value={selectedReason}
                                            onChange={handleReasonChange}
                                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                                        >
                                            <option value="">-- Select a Reason --</option>
                                            {reasonOptions.map((reason) => (
                                                <option key={reason} value={reason}>
                                                    {reason}
                                                </option>
                                            ))}
                                        </select>
                                        {errors.reason && (
                                            <p className="text-sm text-red-600 mt-1">{errors.reason[0]}</p>
                                        )}
                                    </div>

                                    {/* Custom Reason Textarea */}
                                    {selectedReason === "Others" && (
                                        <div className="mb-4">
                                            <textarea
                                                value={customReason}
                                                onChange={(e) => setCustomReason(e.target.value)}
                                                placeholder="Please provide your custom reason..."
                                                rows="4"
                                                className="w-full px-3 py-2 border-2 border-primary rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent resize-none"
                                            />
                                            <div className="text-right text-sm text-gray-500 mt-1">
                                                {customReason.length}/500 characters
                                            </div>
                                            {errors.customReason && (
                                                <p className="text-sm text-red-600 mt-1">{errors.customReason[0]}</p>
                                            )}
                                        </div>
                                    )}

                                    {/* Policy Checkboxes */}
                                    <div className="mb-4 space-y-3">
                                        <label className="flex items-start space-x-3">
                                            <Checkbox
                                                checked={agreePolicy}
                                                onChange={(e) => setAgreePolicy(e.target.checked)}
                                                className="mt-1"
                                            />
                                            <span className="text-sm text-gray-700">
                                                I have read and agree to the{" "}
                                                <a href="#" className="text-primary hover:underline">
                                                    Domain Cancellation Policy
                                                </a>
                                                .
                                            </span>
                                        </label>
                                        <label className="flex items-start space-x-3">
                                            <Checkbox
                                                checked={agreeGrace}
                                                onChange={(e) => setAgreeGrace(e.target.checked)}
                                                className="mt-1"
                                            />
                                            <span className="text-sm text-gray-700">
                                                I understand the{" "}
                                                <a href="#" className="text-primary hover:underline">
                                                    5-Day Grace Period Policy
                                                </a>
                                                .
                                            </span>
                                        </label>
                                        {errors.policy && (
                                            <p className="text-sm text-red-600">{errors.policy[0]}</p>
                                        )}
                                    </div>

                                    {/* Support Contact */}
                                    <div className="mb-6 text-sm text-gray-600">
                                        <p className="font-medium">Need help?</p>
                                        <p><EMAIL></p>
                                        <p>+63 912 345 6789</p>
                                    </div>
                                </div>

                                {/* Action Buttons */}
                                <div className="flex justify-end space-x-3">
                                    <SecondaryButton
                                        onClick={handleClose}
                                        disabled={submitting}
                                        className="px-6"
                                    >
                                        CANCEL
                                    </SecondaryButton>
                                    <DangerButton
                                        onClick={handleSubmit}
                                        disabled={!isValid || !selectedReason || !agreePolicy || !agreeGrace || submitting}
                                        processing={submitting}
                                        className="px-6"
                                    >
                                        {submitting ? "DELETING..." : "DELETE DOMAIN"}
                                    </DangerButton>
                                </div>
                            </Dialog.Panel>
                        </Transition.Child>
                    </div>
                </div>
            </Dialog>
        </Transition>
    );
}

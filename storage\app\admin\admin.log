[2025-08-27 03:08:29] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-08-27 03:20:45] local.ERROR: {"query":[],"parameter":{"domainName":"intrusivetot.com","userEmail":"<EMAIL>","domainId":120,"createdDate":"2025-08-27 01:21:06","userID":7,"reason":"test est setst set set"},"error":"Illuminate\\Database\\QueryException","message":"SQLSTATE[42703]: Undefined column: 7 ERROR:  column \"created_at\" of relation \"domain_cancellation_requests\" does not exist
LINE 1: ...te\", \"support_note\", \"deleted_at\", \"is_refunded\", \"created_a...
                                                             ^ (Connection: client, SQL: insert into \"domain_cancellation_requests\" (\"user_id\", \"domain_id\", \"reason\", \"requested_at\", \"support_agent_id\", \"support_agent_name\", \"feedback_date\", \"support_note\", \"deleted_at\", \"is_refunded\", \"created_at\", \"updated_at\") values (7, 120, test est setst set set, 2025-08-27 03:20:45, 1, admin 1 (a@a.a), 2025-08-27 03:20:45, Request delete created by a@a.a, 2025-08-27 03:20:45, 0, 2025-08-27 03:20:45, 2025-08-27 03:20:45))","code":"42703"}  
[2025-08-27 03:21:00] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-08-27 03:21:48] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-08-27 03:24:01] local.INFO: ApprovalDeleteRequest: Running...  
[2025-08-27 03:24:01] local.INFO: ApprovalDeleteRequest: Processed 4 expired requests  
[2025-08-27 03:24:01] local.INFO: ApprovalDeleteRequest: Done  
[2025-08-27 03:24:06] local.INFO: Domain limittestme.com has clientDeleteProhibited status, cannot delete  
[2025-08-27 03:24:06] local.ERROR: Domain limittestme.com has clientDeleteProhibited status  
[2025-08-27 03:24:06] local.INFO: number of attempts: 1  
[2025-08-27 03:24:06] local.ERROR: Domain limittestme.com has clientDeleteProhibited status  
[2025-08-27 03:24:06] local.ERROR: {"query":[],"parameter":[],"error":"Exception","message":"Domain limittestme.com has clientDeleteProhibited status","code":0}  
[2025-08-27 03:24:08] local.INFO: Domain limittestme.net has clientDeleteProhibited status, cannot delete  
[2025-08-27 03:24:08] local.ERROR: Domain limittestme.net has clientDeleteProhibited status  
[2025-08-27 03:24:08] local.INFO: number of attempts: 1  
[2025-08-27 03:24:08] local.ERROR: Domain limittestme.net has clientDeleteProhibited status  
[2025-08-27 03:24:08] local.ERROR: {"query":[],"parameter":[],"error":"Exception","message":"Domain limittestme.net has clientDeleteProhibited status","code":0}  
[2025-08-27 03:25:02] local.INFO: ApprovalDeleteRequest: Running...  
[2025-08-27 03:25:02] local.INFO: ApprovalDeleteRequest: Processed 2 expired requests  
[2025-08-27 03:25:02] local.INFO: ApprovalDeleteRequest: Done  
[2025-08-27 03:25:04] local.INFO: Domain limittestme.com has clientDeleteProhibited status, cannot delete  
[2025-08-27 03:25:04] local.ERROR: Domain limittestme.com has clientDeleteProhibited status  
[2025-08-27 03:25:04] local.INFO: number of attempts: 1  
[2025-08-27 03:25:04] local.ERROR: Domain limittestme.com has clientDeleteProhibited status  
[2025-08-27 03:25:04] local.ERROR: {"query":[],"parameter":[],"error":"Exception","message":"Domain limittestme.com has clientDeleteProhibited status","code":0}  
[2025-08-27 03:25:07] local.INFO: Domain limittestme.net has clientDeleteProhibited status, cannot delete  
[2025-08-27 03:25:07] local.ERROR: Domain limittestme.net has clientDeleteProhibited status  
[2025-08-27 03:25:07] local.INFO: number of attempts: 1  
[2025-08-27 03:25:07] local.ERROR: Domain limittestme.net has clientDeleteProhibited status  
[2025-08-27 03:25:07] local.ERROR: {"query":[],"parameter":[],"error":"Exception","message":"Domain limittestme.net has clientDeleteProhibited status","code":0}  
[2025-08-27 05:02:13] local.INFO: Domain History: Domain deletion request approved by admin 1 (a@a.a)  
[2025-08-27 05:02:24] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-08-27 05:03:42] local.INFO: Domain History: Domain deletion request approved by admin 1 (a@a.a)  
[2025-08-27 05:10:07] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-08-27 05:24:01] local.INFO: ApprovalDeleteRequest: Running...  
[2025-08-27 05:24:01] local.INFO: ApprovalDeleteRequest: Processed 4 expired requests  
[2025-08-27 05:24:01] local.INFO: ApprovalDeleteRequest: Done  
[2025-08-27 05:24:07] local.INFO: Domain limittestme.com has clientDeleteProhibited status, cannot delete  
[2025-08-27 05:24:07] local.ERROR: SQLSTATE[42703]: Undefined column: 7 ERROR:  column "updated_at" of relation "domain_cancellation_requests" does not exist
LINE 1: ...te" = $3, "support_note" = $4, "deleted_at" = $5, "updated_a...
                                                             ^ (Connection: client, SQL: update "domain_cancellation_requests" set "support_agent_id" = ?, "support_agent_name" = ?, "feedback_date" = ?, "support_note" = Deletion request reverted due to EPP failure: limittestme.com, "deleted_at" = ?, "updated_at" = 2025-08-27 05:24:07 where "domain_id" = 111)  
[2025-08-27 05:24:07] local.INFO: number of attempts: 1  
[2025-08-27 05:24:07] local.ERROR: SQLSTATE[42703]: Undefined column: 7 ERROR:  column "updated_at" of relation "domain_cancellation_requests" does not exist
LINE 1: ...te" = $3, "support_note" = $4, "deleted_at" = $5, "updated_a...
                                                             ^ (Connection: client, SQL: update "domain_cancellation_requests" set "support_agent_id" = ?, "support_agent_name" = ?, "feedback_date" = ?, "support_note" = Deletion request reverted due to EPP failure: limittestme.com, "deleted_at" = ?, "updated_at" = 2025-08-27 05:24:07 where "domain_id" = 111)  
[2025-08-27 05:24:07] local.ERROR: {"query":[],"parameter":[],"error":"Illuminate\\Database\\QueryException","message":"SQLSTATE[42703]: Undefined column: 7 ERROR:  column \"updated_at\" of relation \"domain_cancellation_requests\" does not exist
LINE 1: ...te\" = $3, \"support_note\" = $4, \"deleted_at\" = $5, \"updated_a...
                                                             ^ (Connection: client, SQL: update \"domain_cancellation_requests\" set \"support_agent_id\" = ?, \"support_agent_name\" = ?, \"feedback_date\" = ?, \"support_note\" = Deletion request reverted due to EPP failure: limittestme.com, \"deleted_at\" = ?, \"updated_at\" = 2025-08-27 05:24:07 where \"domain_id\" = 111)","code":"42703"}  
[2025-08-27 05:24:09] local.INFO: Domain limittestme.net has clientDeleteProhibited status, cannot delete  
[2025-08-27 05:24:09] local.ERROR: SQLSTATE[42703]: Undefined column: 7 ERROR:  column "updated_at" of relation "domain_cancellation_requests" does not exist
LINE 1: ...te" = $3, "support_note" = $4, "deleted_at" = $5, "updated_a...
                                                             ^ (Connection: client, SQL: update "domain_cancellation_requests" set "support_agent_id" = ?, "support_agent_name" = ?, "feedback_date" = ?, "support_note" = Deletion request reverted due to EPP failure: limittestme.net, "deleted_at" = ?, "updated_at" = 2025-08-27 05:24:09 where "domain_id" = 112)  
[2025-08-27 05:24:09] local.INFO: number of attempts: 1  
[2025-08-27 05:24:09] local.ERROR: SQLSTATE[42703]: Undefined column: 7 ERROR:  column "updated_at" of relation "domain_cancellation_requests" does not exist
LINE 1: ...te" = $3, "support_note" = $4, "deleted_at" = $5, "updated_a...
                                                             ^ (Connection: client, SQL: update "domain_cancellation_requests" set "support_agent_id" = ?, "support_agent_name" = ?, "feedback_date" = ?, "support_note" = Deletion request reverted due to EPP failure: limittestme.net, "deleted_at" = ?, "updated_at" = 2025-08-27 05:24:09 where "domain_id" = 112)  
[2025-08-27 05:24:09] local.ERROR: {"query":[],"parameter":[],"error":"Illuminate\\Database\\QueryException","message":"SQLSTATE[42703]: Undefined column: 7 ERROR:  column \"updated_at\" of relation \"domain_cancellation_requests\" does not exist
LINE 1: ...te\" = $3, \"support_note\" = $4, \"deleted_at\" = $5, \"updated_a...
                                                             ^ (Connection: client, SQL: update \"domain_cancellation_requests\" set \"support_agent_id\" = ?, \"support_agent_name\" = ?, \"feedback_date\" = ?, \"support_note\" = Deletion request reverted due to EPP failure: limittestme.net, \"deleted_at\" = ?, \"updated_at\" = 2025-08-27 05:24:09 where \"domain_id\" = 112)","code":"42703"}  
[2025-08-27 05:25:01] local.INFO: ApprovalDeleteRequest: Running...  
[2025-08-27 05:25:01] local.INFO: ApprovalDeleteRequest: Processed 0 expired requests  
[2025-08-27 05:25:01] local.INFO: ApprovalDeleteRequest: Done  
[2025-08-27 05:26:00] local.INFO: ApprovalDeleteRequest: Running...  
[2025-08-27 05:26:01] local.INFO: ApprovalDeleteRequest: Processed 0 expired requests  
[2025-08-27 05:26:01] local.INFO: ApprovalDeleteRequest: Done  
[2025-08-27 05:27:00] local.INFO: ApprovalDeleteRequest: Running...  
[2025-08-27 05:27:00] local.INFO: ApprovalDeleteRequest: Processed 0 expired requests  
[2025-08-27 05:27:00] local.INFO: ApprovalDeleteRequest: Done  
[2025-08-27 05:31:46] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-08-28 05:33:05] local.INFO: user login from 127.0.0.1  
[2025-08-28 05:34:30] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-08-28 05:41:24] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-08-28 05:44:17] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-08-28 05:44:37] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-08-28 05:46:10] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-08-28 05:46:26] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-08-28 05:48:00] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-08-28 05:49:48] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-08-28 05:49:52] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-08-28 06:01:54] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-08-28 06:02:56] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-08-28 06:05:50] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-08-28 06:06:05] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-08-28 06:06:51] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-08-28 06:07:05] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-08-28 06:12:14] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-08-28 06:12:17] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-08-28 06:14:40] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-08-28 06:14:48] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-08-28 06:27:07] local.INFO: Domain History: Domain deletion request approved by admin 1 (a@a.a)  
